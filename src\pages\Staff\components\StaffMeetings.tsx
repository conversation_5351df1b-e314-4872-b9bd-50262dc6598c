import React, { useState, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Plus,
  Video,
  Users,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  X,
} from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { Loading } from "@/components/ui/loaders";
import { useProjectListWithMeetingTask } from "@/hooks/queries/project";
import { ProjectItemWithTask } from "@/types/project";
import { ProjectTask } from "@/types/task";
import CreateMeetingForm from "./CreateMeetingForm";
// import BulkMeetingCreation from "./BulkMeetingCreation";

// Import types from the form components
interface MeetingFormData {
  title: string;
  projectId: string;
  milestoneId: string;
  council: string;
  date: Date | undefined;
  startTime: string;
  endTime: string;
  meetingLink: string;
}

// Default image URL for projects without logo
const DEFAULT_PROJECT_IMAGE =
  "https://t3.ftcdn.net/jpg/04/72/54/68/360_F_472546867_4MBw9cVFYE7AwnrIIbmZ8xXS0V3mrIzr.jpg";

const StaffMeetings: React.FC = () => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [expandedProjects, setExpandedProjects] = useState<Set<string>>(
    new Set()
  );

  // API call to get projects with meeting tasks
  const {
    data: projectsData,
    isLoading,
    error,
  } = useProjectListWithMeetingTask({
    title: "",
    genres: ["proposal"],
    statuses: ["inprogress"],
    "page-index": currentPage,
    "page-size": pageSize,
  });

  // Extract projects from API response
  const projects = useMemo(() => {
    return projectsData?.["data-list"] || [];
  }, [projectsData]);

  // Toggle project expansion
  const toggleProjectExpansion = (projectId: string) => {
    setExpandedProjects((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(projectId)) {
        newSet.delete(projectId);
      } else {
        newSet.add(projectId);
      }
      return newSet;
    });
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Handler functions
  const handleJoinMeeting = (meetingUrl: string) => {
    if (meetingUrl) {
      window.open(meetingUrl, "_blank");
      toast.success("Joining meeting");
    } else {
      toast.error("No meeting URL available");
    }
  };

  const handleTaskAction = (task: ProjectTask) => {
    toast.info(`Action for task: ${task.name}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loading />
        <span className="ml-2 text-gray-500">Loading projects...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-2">Error loading projects</div>
        <p className="text-gray-600">Please try again later</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Staff Meetings
          </h1>
          <p className="text-gray-600">
            Manage project meetings and tasks across all projects
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" className="w-full sm:w-auto">
              <Plus className="w-4 h-4 sm:mr-2" />
              <span className="hidden sm:inline">Create Meeting</span>
              <span className="sm:hidden">Create</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Meeting</DialogTitle>
              <DialogDescription>
                Schedule a new staff meeting for project review
              </DialogDescription>
            </DialogHeader>
            <CreateMeetingForm
              onSubmit={(data: MeetingFormData) => {
                console.log("Meeting data:", data);
                setIsCreateDialogOpen(false);
                toast.success("Meeting created successfully");
              }}
              onCancel={() => setIsCreateDialogOpen(false)}
              isSubmitting={false}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Projects with Meeting Tasks */}
      <div className="space-y-4">
        {projects.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="text-gray-500 mb-2">No projects found</div>
              <p className="text-sm text-gray-400">
                No projects with meeting tasks available
              </p>
            </CardContent>
          </Card>
        ) : (
          projects.map((project) => (
            <ProjectCard
              key={project.id}
              project={project}
              isExpanded={expandedProjects.has(project.id)}
              onToggleExpansion={() => toggleProjectExpansion(project.id)}
              onJoinMeeting={handleJoinMeeting}
              onTaskAction={handleTaskAction}
            />
          ))
        )}
      </div>

      {/* Pagination */}
      {projectsData && projectsData["total-page"] > 1 && (
        <div className="flex justify-center items-center gap-2 mt-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="text-sm text-gray-600">
            Page {currentPage} of {projectsData["total-page"]}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === projectsData["total-page"]}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  );
};

// ProjectCard Component
interface ProjectCardProps {
  project: ProjectItemWithTask;
  isExpanded: boolean;
  onToggleExpansion: () => void;
  onJoinMeeting: (meetingUrl: string) => void;
  onTaskAction: (task: ProjectTask) => void;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  isExpanded,
  onToggleExpansion,
  onJoinMeeting,
  onTaskAction,
}) => {
  const getImageSrc = (logoUrl: string | null) => {
    return logoUrl || DEFAULT_PROJECT_IMAGE;
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = DEFAULT_PROJECT_IMAGE;
  };

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        {/* Project Header */}
        <div
          className="p-6 cursor-pointer hover:bg-gray-50 transition-colors"
          onClick={onToggleExpansion}
        >
          <div className="flex items-start gap-4">
            {/* Project Logo */}
            <div className="flex-shrink-0">
              <img
                src={getImageSrc(project["logo-url"])}
                alt={project["english-title"]}
                className="w-16 h-16 rounded-lg object-cover border"
                onError={handleImageError}
              />
            </div>

            {/* Project Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-semibold text-gray-900 truncate">
                    {project["english-title"]}
                  </h3>
                  <p className="text-sm text-gray-600 truncate">
                    {project["vietnamese-title"]}
                  </p>
                  <div className="flex items-center gap-4 mt-2">
                    <Badge variant="outline" className="text-xs">
                      {project.type}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {project.category}
                    </Badge>
                    {project.duration && (
                      <span className="text-xs text-gray-500">
                        Duration: {project.duration} months
                      </span>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2 ml-4">
                  {project.tasks && project.tasks.length > 0 && (
                    <Badge className="bg-blue-100 text-blue-800">
                      {project.tasks.length} meeting
                      {project.tasks.length !== 1 ? "s" : ""}
                    </Badge>
                  )}
                  {isExpanded ? (
                    <ChevronUp className="w-5 h-5 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-400" />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Meeting Tasks (Expanded) */}
        {isExpanded && project.tasks && project.tasks.length > 0 && (
          <div className="border-t bg-gray-50">
            <div className="p-6">
              <h4 className="text-sm font-medium text-gray-900 mb-4">
                Meeting Tasks
              </h4>
              <div className="space-y-3">
                {project.tasks.map((task) => (
                  <div
                    key={task.id}
                    className="bg-white rounded-lg border p-4 hover:shadow-sm transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h5 className="font-medium text-gray-900 truncate">
                          {task.name}
                        </h5>
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {task.description}
                        </p>
                        <div className="flex items-center gap-4 mt-2">
                          <span className="text-xs text-gray-500">
                            Start:{" "}
                            {new Date(task["start-date"]).toLocaleDateString()}
                          </span>
                          <span className="text-xs text-gray-500">
                            End:{" "}
                            {new Date(task["end-date"]).toLocaleDateString()}
                          </span>
                          <Badge
                            variant={
                              task.status === "completed"
                                ? "default"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {task.status}
                          </Badge>
                        </div>
                        {task["member-tasks"] &&
                          task["member-tasks"].length > 0 && (
                            <div className="flex items-center gap-1 mt-2">
                              <Users className="w-3 h-3 text-gray-400" />
                              <span className="text-xs text-gray-500">
                                {task["member-tasks"].length} member
                                {task["member-tasks"].length !== 1 ? "s" : ""}
                              </span>
                            </div>
                          )}
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        {task["meeting-url"] ? (
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1"
                            onClick={() => onJoinMeeting(task["meeting-url"]!)}
                          >
                            <Video className="w-3 h-3" />
                            Join
                            <ExternalLink className="w-3 h-3" />
                          </Button>
                        ) : (
                          <span className="text-xs text-gray-500">
                            No meeting link
                          </span>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onTaskAction(task)}
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* No Tasks Message */}
        {isExpanded && (!project.tasks || project.tasks.length === 0) && (
          <div className="border-t bg-gray-50 p-6 text-center">
            <p className="text-sm text-gray-500">
              No meeting tasks found for this project
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StaffMeetings;
